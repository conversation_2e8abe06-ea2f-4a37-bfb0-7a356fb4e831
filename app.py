from selenium import webdriver
# from seleniumwire import webdriver  # DISABLED to avoid socket issues
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support.ui import Select
from selenium.webdriver.common.by import By
from selenium.webdriver.common.proxy import Proxy, ProxyType
from selenium.common.exceptions import *
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
import time
import random
import datetime
import calendar
import requests
import csv
import string
from fp.fp import FreeProxy
from fake_useragent import UserAgent

# Option for Auto User info generation
AUTO_GENERATE_UERINFO = True
AUTO_GENERATE_NUMBER = 10

# Include Refer URL
INCLUDE_REFER_URL = False

# Time to wait for SELECTORS.(second)
WAIT = 4

# Max Retry to get phone number from sms-activate.org
REQUEST_MAX_TRY = 10

# Your SMS-Activate API key
API_KEY = "8e49fdB90d0209c085dd1df56cedf00e" #9b6b9eb50d0A30---------d9b7495b
COUNTRY_CODE = "175" #i.e, Austrailian country code, See country table in sms-activate. I often use Australian phone number and it works almost always.

sms_activate_url = "https://sms-activate.org/stubs/handler_api.php"
phone_request_params = {
    "api_key":API_KEY,
    "action":"getNumber",
    "country":COUNTRY_CODE, 
    "service":"go",
}

status_param = {
    "api_key":API_KEY,
    "action":"getStatus"
}

SELECTORS = {
    "create_account":[
        "//span[contains(text(),'Create account')]",
        "//a[contains(text(),'Create account')]",
        "//button[contains(text(),'Create account')]",
        "//*[contains(text(),'Create account')]",
        "//span[contains(text(),'Create')]",
        "//a[contains(@href,'signup')]",
        "//button[@class='VfPpkd-LgbsSe VfPpkd-LgbsSe-OWXEXe-dgl2Hf ksBjEc lKxP2d LQeN7 FliLIb uRo0Xe TrZEUc Xf9GD']",
        "//*[@class='JnOM6e TrZEUc kTeh9 KXbQ4b']"
        ],
    'for_my_personal_use':[
        "//span[contains(text(),'For my personal use')]",
        "//div[contains(text(),'For my personal use')]",
        "//*[contains(text(),'personal use')]",
        "//*[contains(text(),'Personal')]",
        "//span[@class='VfPpkd-StrnGf-rymPhb-b9t22c']",
        ],
    "first_name":[
        "//*[@name='firstName']",
        "//*[@id='firstName']",
        "//input[@name='firstName']",
        "//input[contains(@placeholder,'First')]"
    ],
    "last_name":[
        "//*[@name='lastName']",
        "//*[@id='lastName']",
        "//input[@name='lastName']",
        "//input[contains(@placeholder,'Last')]"
    ],
    "username":[
        "//*[@name='Username']",
        "//*[@id='Username']",
        "//input[@name='Username']",
        "//input[contains(@placeholder,'username')]"
    ],
    "password":[
        "//*[@name='Passwd']",
        "//*[@id='passwd']",
        "//input[@name='Passwd']",
        "//input[@type='password']"
    ],
    "confirm_password":[
        "//*[@name='PasswdAgain']",
        "//*[@name='ConfirmPasswd']",
        "//input[@name='PasswdAgain']",
        "//input[contains(@placeholder,'Confirm')]"
    ],
    "next":[
            "//span[contains(text(),'Next')]",
            "//button[contains(text(),'Next')]",
            "//*[contains(text(),'Next')]",
            "//span[contains(text(),'Continue')]",
            "//button[contains(text(),'Continue')]",
            "//span[contains(text(),'I agree')]",
            "//button[contains(text(),'I agree')]",
            "//*[contains(text(),'I agree')]",
            "//button[@class='VfPpkd-LgbsSe VfPpkd-LgbsSe-OWXEXe-k8QpJ VfPpkd-LgbsSe-OWXEXe-dgl2Hf nCP5yc AjY5Oe DuMIQc LQeN7 qIypjc TrZEUc lw1w4b']"
    ],
    "phone_number":[
        "//*[@id='phoneNumberId']",
        "//input[@name='phoneNumber']",
        "//input[contains(@placeholder,'phone')]"
    ],
    "code":[
        '//input[@name="code"]',
        '//input[@id="code"]',
        '//input[contains(@placeholder,"code")]'
    ],
    "acc_phone_number":[
        '//input[@id="phoneNumberId"]',
        '//input[@name="phoneNumber"]'
    ],
    "acc_day":[
        '//input[@name="day"]',
        '//input[@id="day"]'
    ],
    "acc_month":[
        '//select[@id="month"]',
        '//select[@name="month"]',
        '//*[@id="month"]',
        '//*[@name="month"]',
        '//*[contains(@aria-label,"Month")]',
        '//*[contains(@placeholder,"Month")]',
        '//div[contains(@class,"month")]',
        '//*[@role="combobox"][contains(@aria-label,"month")]'
    ],
    "acc_year":[
        '//input[@name="year"]',
        '//input[@id="year"]'
    ],
    "acc_gender":[
        '//select[@id="gender"]',
        '//select[@name="gender"]',
        '//*[@id="gender"]',
        '//*[@name="gender"]',
        '//*[contains(@aria-label,"Gender")]',
        '//*[contains(@placeholder,"Gender")]',
        '//div[contains(@class,"gender")]',
        '//*[@role="combobox"][contains(@aria-label,"gender")]'
    ],
    "username_warning":[
        '//*[@class="jibhHc"]',
        '//*[contains(text(),"already taken")]',
        '//*[contains(text(),"not available")]'
    ],
    "username_select":[
        '//*[@aria-posinset="3"]',
        '//div[contains(@class,"suggestion")]'
    ]
}
# https://webflow.com/made-in-webflow/fast , I tried to find the fast websites and you can add more.
SITE_LIST = [
    'https://google.com',
    'https://wizardrytechnique.webflow.io/',
    'https://www.rachelbavaresco.com/',
    'https://lightning-bolt.webflow.io/'
]
proxy_list = None
with open("./data/Proxy_DB.csv", 'r') as proxy_list_file:
    proxy_list = csv.reader(proxy_list_file)
    proxy_list = list(proxy_list)

def generatePassword():
    chars = string.ascii_uppercase + string.ascii_lowercase + string.digits + string.punctuation
    size = random.randint(8, 12)
    return ''.join(random.choice(chars) for x in range(size))

def getRandomeUserAgent():
    UAGENTS = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36 Edg/106.0.1370.52',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 YaBrowser/21.8.1.468 Yowser/2.5 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:106.0) Gecko/20100101 Firefox/106.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:105.0) Gecko/20100101 Firefox/105.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64; rv:105.0) Gecko/20100101 Firefox/105.0',
        'Mozilla/5.0 (X11; CrOS x86_64 14440.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4807.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14467.0.2022) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4838.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14469.7.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.13 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14455.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4827.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14469.11.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.17 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14436.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4803.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14475.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4840.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14469.3.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.9 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14471.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4840.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14388.37.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.9 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14409.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4829.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14395.0.2021) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4765.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14469.8.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.14 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14484.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4840.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14450.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4817.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14473.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4840.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14324.72.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.91 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14454.0.2022) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4824.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14453.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4816.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14447.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4815.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14477.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4840.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14476.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4840.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14469.8.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.9 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14588.67.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.41 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14588.67.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14526.69.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.82 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14695.25.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.22 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14526.89.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.133 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14526.57.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.64 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14526.89.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.133 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14526.84.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.93 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14469.59.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.41 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14588.91.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.55 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14695.23.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.20 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14695.36.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.36 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14588.41.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.26 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14695.11.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.6 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14588.67.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.41 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14685.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.4992.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14526.69.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.82 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14682.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.16 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14695.9.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.5 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14574.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4937.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14388.52.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14716.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5002.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14268.81.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14469.41.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.48 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14388.61.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14695.37.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.37 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14588.51.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.32 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14526.89.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.133 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14588.92.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.56 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14526.43.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.54 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14505.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4870.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14526.16.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.25 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14526.28.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.44 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14543.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4918.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14588.11.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.6 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14526.89.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.133 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14588.31.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.19 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14526.6.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.13 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14658.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.4975.0 Safari/537.36',
        'Mozilla/5.0 (X11; CrOS x86_64 14695.25.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5002.0 Safari/537.36'
    ]
    agent = random.choice(UAGENTS)
    return agent

# This method is for chrome driver initialization. You can customize if you want.
def setDriver():
    # DISABLE SELENIUM-WIRE TO AVOID SOCKET ISSUES
    # seleniumwire_options = {}
    # seleniumwire_options['exclude_hosts'] = ['google-analytics.com']

    # Secure Connection
    # seleniumwire_options['verify_ssl'] = True

    # Set Proxy - DISABLED SOCKS PROXY TO AVOID AUTHENTICATION ISSUES
    # proxy = getProxy() # Rotating proxy
    # SOCKS_PROXY = "socks5://14ab1e7131541:39d813de77@**************:12324" # Fixed proxy - DISABLED
    # SOCKS_PROXY = "socks5://user:pass@ip:port" # Fixed proxy - DISABLED
    # SOCKS_PROXY = 'socks5://**************:59166' # DISABLED

    # Disable proxy usage to avoid SOCKS5 authentication failures
    print('################ Running without proxy and selenium-wire to avoid issues ################')

    # Comment out proxy usage for now
    # try:
    #     random_proxy = FreeProxy(timeout=1).get()
    #     print('################ Use FreeProxy library to get HTTP proxy ################')
    # except:
    #     print('################ Use Proxy DB to get HTTP proxy ################')
    #     random_proxy = "http://"+ random.choice(proxy_list)[0]

    # HTTP_PROXY = random_proxy
    # print(HTTP_PROXY)
    # HTTPS_PROXY = "********************:port"

    # Proxy - DISABLED
    # proxy_options = {}
    # proxy_options['no_proxy']= 'localhost,127.0.0.1'

    ## Http proxy - DISABLED
    # proxy_options['http'] = HTTP_PROXY

    ## Https proxy - DISABLED
    # proxy_options['https'] = HTTPS_PROXY

    ## Socks proxy - DISABLED
    # proxy_options['http'] = SOCKS_PROXY
    # proxy_options['https'] = SOCKS_PROXY

    # seleniumwire_options['proxy'] = proxy_options
    # prox = Proxy()
    # prox.proxy_type = ProxyType.MANUAL
    # prox.socks_proxy = SOCKS_PROXY
    # prox.socks_version = 5
    # prox.http_proxy = HTTP_PROXY
    # print(SOCKS_PROXY)
    # prox.http_proxy = SOCKS_PROXY
    # prox.https_proxy = SOCKS_PROXY

    # capabilities = webdriver.DesiredCapabilities.CHROME
    # prox.add_to_capabilities(capabilities)

    # Set User Agent
    # user_agent = getRandomeUserAgent() # Random user agent
    # user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" # Fixed agent
    # Please refer to this https://github.com/fake-useragent/fake-useragent
    try:
        user_agent = UserAgent(fallback="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36").random
    except:
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    print(user_agent)

    # Set Browser Option
    options = ChromeOptions()
    # options = FirefoxOptions()

    prefs = {
        "profile.password_manager_enabled": False,
        "credentials_enable_service": False,
        "useAutomationExtension": False,
        "profile.default_content_setting_values.notifications": 2
    }
    options.add_experimental_option("prefs", prefs)
    options.add_experimental_option("useAutomationExtension", False)
    options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
    # Basic stability options
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument("--disable-popup-blocking")
    options.add_argument("--disable-notifications")
    options.add_argument('--ignore-ssl-errors=yes')
    options.add_argument('--ignore-certificate-errors')
    options.add_argument('--no-first-run')
    options.add_argument('--no-default-browser-check')
    options.add_argument('--disable-default-apps')
    options.add_argument('--disable-extensions')
    options.add_argument('--disable-plugins')

    # Add headless mode for better stability
    options.add_argument('--headless=new')  # Use new headless mode for better stability
    print("################ Running in headless mode for better stability ################")
    # options.add_argument('--disable-images')  # Keep images for better compatibility
    # options.add_argument('--disable-javascript')  # Keep JavaScript for Gmail functionality

    # options.add_argument('--headless') # UI
    # options.add_argument("--incognito")
    # options.add_argument(r"--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data")
    # options.add_argument(r'--profile-directory=ProfileName')
    options.add_argument(f"user-agent={user_agent}")

    # Use regular selenium instead of selenium-wire to avoid socket issues
    try:
        from selenium import webdriver as regular_webdriver
        driver = regular_webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
    except:
        # Fallback to selenium-wire if regular selenium fails
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

    return driver

def main():
    user_number = 0
    i = 0

    if(AUTO_GENERATE_UERINFO):
        user_number = AUTO_GENERATE_NUMBER
        print('################ Open First_Name_DB.csv ################')
        try:
            first_name_file = open("./data/First_Name_DB.csv", 'r')
            first_names = csv.reader(first_name_file)
            first_names = list(first_names)
        except:
            print('################ Please check if First_Name_DB.csv exists ################')
            quit()

        print('################ Open Last_Name_DB.csv ################')
        try:
            last_name_file = open("./data/Last_Name_DB.csv", 'r')
            last_names = csv.reader(last_name_file)
            last_names = list(last_names)
        except:
            print('################ Please check if Last_Name_DB.csv exists ################')
            quit()
    else:
        print('################ Open User.csv ################')
        try:
            user_info_file = open("User.csv", 'r')
            user_infos = csv.reader(user_info_file)
            user_infos = list(user_infos)
            user_number = len(user_infos)
        except:
            print('################ Please check if User.csv exists ################')
            quit()

    while True:
        try:
            # Check if the count reach to the maxium users.
            
            if i == user_number:
                break

            i = i + 1
            print('################ User:', i,' ################')
            if AUTO_GENERATE_UERINFO:
                first_name = random.choice(first_names)[0]
                last_name = random.choice(last_names)[0]
                password = generatePassword()
                birthday = str(random.randint(1,12)) + "/" + str(random.randint(1,28)) + "/" +  str(random.randint(1980,1999))
                user_name_manual = ""
                print(first_name + "\t" + last_name + "\t" + password + '\t' + birthday)
            else:
                row = user_infos[i]
                if "Firstname" == row[0]:
                    continue

                first_name = row[0]
                last_name = row[1]
                password = row[2]
                birthday = row[3]
                print(first_name + "\t" + last_name + "\t" + password + '\t' + birthday)
            try:
                user_name_manual = row[4]
            except:
                user_name_manual = ""

            print('################ Initialize Chrome Driver ################')
            driver = None
            driver_attempts = 0
            max_driver_attempts = 3

            while driver_attempts < max_driver_attempts:
                try:
                    driver = setDriver()
                    print("################ Chrome Driver initialized successfully ################")
                    break
                except Exception as e:
                    driver_attempts += 1
                    print(f"################ Driver attempt {driver_attempts} failed: {e} ################")
                    if driver_attempts >= max_driver_attempts:
                        print("################ Max driver attempts reached, skipping user ################")
                        break
                    time.sleep(5)  # Wait before retry

            if driver is None:
                continue

            print('################ Random Refer website to bypass Google Bot Detection ################')
            if INCLUDE_REFER_URL:
                random_url = random.choice(SITE_LIST)
                driver.get(random_url)

            # 4 ways to go to account creation page.
            random_int = random.randint(1,4)
            if random_int ==  1:

                print('################ Creat a google account article ################')
                driver.get('https://support.google.com/accounts/answer/27441?hl=en')
                WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH,'//*[@id="hcfe-content"]/section/div/div[1]/article/section/div/div[1]/div/div[2]/a[1]'))).click()
                time.sleep(WAIT)
            elif random_int == 2:
                print('################ Go to account page ################')
                driver.get("https://accounts.google.com")

                time.sleep(WAIT)
                
                print('################ Click "Create account" ################')
                create_account_clicked = False
                for selector in SELECTORS["create_account"]:
                    try:
                        element = WebDriverWait(driver, WAIT).until(EC.element_to_be_clickable((By.XPATH, selector)))
                        driver.execute_script("arguments[0].click();", element)
                        print(f"Successfully clicked Create account with selector: {selector}")
                        create_account_clicked = True
                        break
                    except Exception as e:
                        print(f"Failed with selector {selector}: {e}")
                        continue

                if not create_account_clicked:
                    print("################ Trying alternative Create account methods ################")
                    try:
                        # Try clicking any link that contains "create" or "sign up"
                        driver.find_element(By.PARTIAL_LINK_TEXT, "Create").click()
                        create_account_clicked = True
                    except:
                        try:
                            driver.find_element(By.PARTIAL_LINK_TEXT, "Sign up").click()
                            create_account_clicked = True
                        except:
                            pass

                time.sleep(WAIT)

                print('################ Click "For my personal use" ################')
                personal_use_clicked = False
                for selector in SELECTORS["for_my_personal_use"]:
                    try:
                        element = WebDriverWait(driver, WAIT).until(EC.element_to_be_clickable((By.XPATH, selector)))
                        driver.execute_script("arguments[0].click();", element)
                        print(f"Successfully clicked For my personal use with selector: {selector}")
                        personal_use_clicked = True
                        break
                    except Exception as e:
                        print(f"Failed with selector {selector}: {e}")
                        continue

                if not personal_use_clicked:
                    print("################ Trying alternative Personal use methods ################")
                    try:
                        # Try clicking any element that contains "personal"
                        driver.find_element(By.XPATH, "//*[contains(text(),'personal')]").click()
                        personal_use_clicked = True
                    except:
                        pass

            elif random_int == 3:
                driver.get('https://accounts.google.com/signup/v2/webcreateaccount?flowName=GlifWebSignIn&flowEntry=SignUp')
                time.sleep(WAIT)
            
            elif random_int == 4:
                driver.get('https://support.google.com/mail/answer/56256?hl=en')
                WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH,'//*[@id="hcfe-content"]/section/div/div[1]/article/section/div/div[1]/div/p[1]/a'))).click()
                time.sleep(WAIT)

            username_try = 0

            # if the username exists, it retries REQUEST_MAX_TRY times.
            while username_try < REQUEST_MAX_TRY:
                time.sleep(WAIT*2)

                print('################ 1st step of Creation Wizard. ################')


                print("################ Generate User Try: ", username_try+1, " ################")
                # set the first name.
                print('################ First Name ################')
                first_name_filled = False
                for selector in SELECTORS['first_name']:
                    try:
                        first_name_tag = WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, selector)))
                        first_name_tag.clear()
                        time.sleep(WAIT/2)
                        print(first_name)
                        first_name_tag.send_keys(first_name)
                        first_name_filled = True
                        break
                    except Exception as e:
                        print(f"Failed to fill first name with selector {selector}: {e}")
                        continue

                if not first_name_filled:
                    print("################ Could not fill first name ################")
                    continue

                # set the surname.
                print('################ Last Name ################')
                last_name_filled = False
                for selector in SELECTORS['last_name']:
                    try:
                        last_name_tag = WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, selector)))
                        last_name_tag.clear()
                        last_name_tag.send_keys(last_name)
                        last_name_filled = True
                        break
                    except Exception as e:
                        print(f"Failed to fill last name with selector {selector}: {e}")
                        continue

                if not last_name_filled:
                    print("################ Could not fill last name ################")
                    continue

                #click next button
                print('################ "Next" ################')
                next_clicked = False
                for selector in SELECTORS['next']:
                    try:
                        element = WebDriverWait(driver, WAIT).until(EC.element_to_be_clickable((By.XPATH, selector)))
                        driver.execute_script("arguments[0].click();", element)
                        print(f"Successfully clicked Next with selector: {selector}")
                        next_clicked = True
                        break
                    except Exception as e:
                        print(f"Failed to click Next with selector {selector}: {e}")
                        continue

                if not next_clicked:
                    print("################ Trying alternative Next button methods ################")
                    try:
                        # Try pressing Enter key
                        from selenium.webdriver.common.keys import Keys
                        driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ENTER)
                        next_clicked = True
                    except:
                        pass
                time.sleep(WAIT*2)

                print('################ 2nd step of Creation Wizard. ################')
                print('################ Birthday & Gender ################')

                # Date
                day_filled = False
                for selector in SELECTORS['acc_day']:
                    try:
                        day_element = WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, selector)))
                        day_element.clear()
                        day_element.send_keys(birthday.split('/')[1])
                        day_filled = True
                        break
                    except Exception as e:
                        print(f"Failed to fill day with selector {selector}: {e}")
                        continue

                # Month - Handle dropdown properly with multiple strategies
                month_filled = False
                month_value = birthday.split('/')[0]
                month_name = calendar.month_name[int(month_value)]
                print(f"Trying to select month: {month_value} ({month_name})")

                # Strategy 1: Try to find and click month input/dropdown
                try:
                    # Look for month input field first
                    month_inputs = [
                        "//input[@name='month']",
                        "//input[@id='month']",
                        "//input[contains(@placeholder,'Month')]",
                        "//input[contains(@aria-label,'Month')]"
                    ]

                    for input_selector in month_inputs:
                        try:
                            month_input = WebDriverWait(driver, 2).until(EC.presence_of_element_located((By.XPATH, input_selector)))
                            month_input.clear()
                            month_input.send_keys(month_value)
                            month_filled = True
                            print(f"Successfully filled month input: {month_value}")
                            break
                        except:
                            continue

                    if not month_filled:
                        # Strategy 2: Try dropdown selection
                        dropdown_selectors = [
                            "//select[@name='month']",
                            "//select[@id='month']",
                            "//*[@role='combobox'][contains(@aria-label,'month')]",
                            "//*[@role='listbox'][contains(@aria-label,'month')]"
                        ]

                        for dropdown_selector in dropdown_selectors:
                            try:
                                dropdown = WebDriverWait(driver, 2).until(EC.element_to_be_clickable((By.XPATH, dropdown_selector)))
                                if dropdown.tag_name == 'select':
                                    Select(dropdown).select_by_value(month_value)
                                else:
                                    dropdown.click()
                                    time.sleep(1)
                                    # Try to find the month option
                                    option_selectors = [
                                        f"//option[@value='{month_value}']",
                                        f"//*[contains(text(),'{month_name}')]",
                                        f"//*[text()='{month_name}']"
                                    ]
                                    for option_selector in option_selectors:
                                        try:
                                            option = driver.find_element(By.XPATH, option_selector)
                                            option.click()
                                            month_filled = True
                                            break
                                        except:
                                            continue
                                if month_filled:
                                    print(f"Successfully selected month from dropdown: {month_name}")
                                    break
                            except:
                                continue

                except Exception as e:
                    print(f"Month selection failed: {e}")

                if not month_filled:
                    print("################ Skipping month - will try to continue ################")

                # Year
                year_filled = False
                for selector in SELECTORS['acc_year']:
                    try:
                        year_element = WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, selector)))
                        year_element.clear()
                        year_element.send_keys(birthday.split('/')[2])
                        year_filled = True
                        break
                    except Exception as e:
                        print(f"Failed to fill year with selector {selector}: {e}")
                        continue

                # Gender - Handle dropdown properly with multiple strategies
                gender_filled = False
                print("Trying to select gender: Male")

                try:
                    # Strategy 1: Try direct gender selection
                    gender_options = [
                        "//*[contains(text(),'Male') and not(contains(text(),'Female'))]",
                        "//*[text()='Male']",
                        "//option[@value='1']",
                        "//option[contains(text(),'Male')]"
                    ]

                    for option_selector in gender_options:
                        try:
                            gender_option = WebDriverWait(driver, 2).until(EC.element_to_be_clickable((By.XPATH, option_selector)))
                            driver.execute_script("arguments[0].click();", gender_option)
                            gender_filled = True
                            print(f"Successfully selected gender option: Male")
                            break
                        except:
                            continue

                    if not gender_filled:
                        # Strategy 2: Try dropdown selection
                        dropdown_selectors = [
                            "//select[@name='gender']",
                            "//select[@id='gender']",
                            "//*[@role='combobox'][contains(@aria-label,'gender')]",
                            "//*[@role='listbox'][contains(@aria-label,'gender')]"
                        ]

                        for dropdown_selector in dropdown_selectors:
                            try:
                                dropdown = WebDriverWait(driver, 2).until(EC.element_to_be_clickable((By.XPATH, dropdown_selector)))
                                if dropdown.tag_name == 'select':
                                    Select(dropdown).select_by_value('1')
                                    gender_filled = True
                                else:
                                    dropdown.click()
                                    time.sleep(1)
                                    # Try to find Male option
                                    male_option = driver.find_element(By.XPATH, "//*[contains(text(),'Male') and not(contains(text(),'Female'))]")
                                    male_option.click()
                                    gender_filled = True
                                if gender_filled:
                                    print(f"Successfully selected gender from dropdown: Male")
                                    break
                            except:
                                continue

                except Exception as e:
                    print(f"Gender selection failed: {e}")

                if not gender_filled:
                    print("################ Skipping gender - will try to continue ################")

               #click next button
                print('################ Click "Next" Buton ################')
                for selector in SELECTORS['next']:
                    try:
                        WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, selector))).click()
                        break
                    except:
                        pass
                time.sleep(WAIT*2)

                # set username
                print('################ Set User Name ################')
                if user_name_manual == "":
                    rand_5_digit_num = random.randint(10000,99999)
                    user_name = first_name +"."+ last_name
                    user_name = user_name.lower() + str(rand_5_digit_num)
                else:
                    user_name = user_name_manual
                try:
                    WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, SELECTORS['username_select']))).click()
                except:
                    pass
                try:
                    user_name_tag = WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, SELECTORS['username'])))
                    user_name_tag.clear()
                    print(user_name)
                    time.sleep(WAIT/2)
                    user_name_tag.send_keys(user_name)
                # time.sleep(WAIT*1000)
                except:
                    pass

                #click next button
                print('################ Click "Next" Buton ################')
                for selector in SELECTORS['next']:
                    try:
                        WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, selector))).click()
                        break
                    except:
                        pass
                time.sleep(WAIT*2)
                print('################ Check Username Validation ################')
                try:
                    WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, SELECTORS['username_warning'])))
                    user_name_manual = ""
                    print("Invalid")
                    username_try = username_try + 1
                    continue
                except:
                    print("Valid")
                    pass

                # set password
                print('################ Set Password ################')
                passwd_tag =WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, SELECTORS['password'])))
                passwd_tag.clear()
                passwd_tag.send_keys(password)

                print('################ Set Confirm Password ################')
                confirmwd_tag = WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, SELECTORS['confirm_password'])))
                confirmwd_tag.clear()
                confirmwd_tag.send_keys(password)

                #click next button
                print('################ Click "Next" Buton ################')
                for selector in SELECTORS['next']:
                    try:
                        WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, selector))).click()
                        break
                    except:
                        pass
                time.sleep(WAIT*2)

                print('################ Check Phone Verification ################')
                without_verification = False
                try:
                    WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, SELECTORS['acc_day'])))
                    without_verification = True
                    print("No. It doesn't require.")
                    break
                except:
                    print("Yes. It requires")
                    pass
                print('################ Input Phone Number ################')
                try:
                    phone_number_input = WebDriverWait(driver, WAIT*3).until(EC.presence_of_element_located((By.XPATH, SELECTORS['phone_number'])))
                    time.sleep(WAIT)
                    break
                except:
                    username_try = username_try + 1
            number = ""
            activationId = ""
            count = 0
            if without_verification == False:
                print('################ Get Phone Number from SMS_Activate ################')
                while(count < REQUEST_MAX_TRY):
                    res = requests.get(url=sms_activate_url,params = phone_request_params)
                    data = res.text
                    print(data)
                    if "ACCESS_NUMBER" in data:
                        activationId = data.split(':')[1]
                        number = data.split(':')[2]
                        
                        number = '+'+ number
                        print(number)
                        break
                    if "NO_BALANCE" in data:
                        print("Check your Balance in sms-activate.")
                        exit()
                    count = count+1
                    time.sleep(WAIT)
                if number == '':
                    print("################ Cannot get phone number: ", REQUEST_MAX_TRY, " times retrial. ################")
                    raise Exception("Go to next account.")
                
                phone_number_input.send_keys(number)

                #click next button
                print('################ Click "Next" Buton ################')
                for selector in SELECTORS['next']:
                    try:
                        WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, selector))).click()
                        break
                    except:
                        pass

                print('################ Get SMS Code from SMS_Activate ################')
                time.sleep(WAIT)

                count_status = 0
                code = ''
                while(True):
                # while(count_status < REQUEST_MAX_TRY):
                    status_param['id'] = activationId
                    print(status_param)
                    res_code = requests.get(url=sms_activate_url,params = status_param)
                    data_code = res_code.text
                    print(data_code)
                    if "STATUS_OK" in data_code:
                        code = data_code.split(':')[1]
                        break

                    count_status = count_status + 1
                    time.sleep(WAIT*5)

                if code == '':
                    print('Cannot receive code from sms_activate: ',REQUEST_MAX_TRY, " times retrial")
                    raise Exception("Go to next account.")

                print('################ Verify Phone Code ################')  
                WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, SELECTORS['code']))).send_keys(code)

                #click next button
                print('################ Click "Verify" Buton ################')
                for selector in SELECTORS['next']:
                    try:
                        WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, selector))).click()
                        break
                    except:
                        pass

            time.sleep(WAIT*2)
            print('################ Clear Account Phone Number ################')
            # WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, SELECTORS['acc_phone_number']))).clear()

            # print('################ Account Birthday ################')
            # # Date   
            # WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, SELECTORS['acc_day']))).send_keys(birthday.split('/')[1])
            
            # # Month
            # select_acc_month = WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, SELECTORS['acc_month'])))

            # acc_month = Select(select_acc_month)
            # acc_month.select_by_value(birthday.split('/')[0])

            # # Year
            # WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, SELECTORS['acc_year']))).send_keys(birthday.split('/')[2])

            # select_acc_gender = WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, SELECTORS['acc_gender'])))

            # # Gender
            # acc_gender = Select(select_acc_gender)
            # acc_gender.select_by_value('1')

            print('################ Click "Next" Buton ################')
            for selector in SELECTORS['next']:
                try:
                    WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, selector))).click()
                    break
                except:
                    pass
            print('################ Click "I agree" Buton ################')
            time.sleep(WAIT)

            # Scroll to click "I agree"
            driver.execute_script("window.scrollTo(0, 800)") 
            time.sleep(WAIT)
            for selector in SELECTORS['next']:
                try:
                    WebDriverWait(driver, WAIT).until(EC.presence_of_element_located((By.XPATH, selector))).click()
                    break
                except:
                    pass
            time.sleep(WAIT*3)
            print('################ Save to Created.txt ################')
            f = open('Created.txt', 'a')
            f.write(user_name + "\t" + password + "\t" +birthday + "\t"+ number + "\n")
            f.close()

            print('################ Successfully created account! ################')
            try:
                driver.quit()
            except:
                pass
        except Exception as e:
            print(f"################ Error during account creation: {e} ################")
            try:
                if driver is not None:
                    driver.quit()
            except:
                pass
            # Wait before trying next account
            time.sleep(5)

    user_info_file.close()
main()